import { Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { PaymentModule } from './payment/payment.module';
import { KycModule } from './kyc/kyc.module';
import { AccountsModule } from './payment/accounts/accounts.module';

@Module({
  imports: [UsersModule, AuthModule, PaymentModule, KycModule, AccountsModule],
})
export class CoreModule {}
